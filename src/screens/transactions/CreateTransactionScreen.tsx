import React, { useState } from 'react';
import { View, Text, StyleSheet, Alert, TouchableOpacity } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigation, useRoute } from '@react-navigation/native';
import uuid from 'react-native-uuid';
import { RootState } from '../../store/store';

import { TransactionType, TransactionStatus } from '../../types/Transaction';
import { Colors } from '../../constants/colors';
import { canCreateTransaction } from '../../utils/permissions';
import TransactionForm from '../../components/forms/TransactionForm';
import TransactionPreviewModal from '../../components/modals/TransactionPreviewModal';
import { apiService, TransactionCreateRequest } from '../../services/apiService';

export default function CreateTransactionScreen() {
  const navigation = useNavigation();
  const route = useRoute<any>();
  const dispatch = useDispatch();
  const { user } = useSelector((state: RootState) => state.auth);
  const { users } = useSelector((state: RootState) => state.users);
  const [loading, setLoading] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [previewData, setPreviewData] = useState<any>(null);
  const selectedType = route.params?.type || TransactionType.SPEND

  if (!user) return null;

  const handleSubmit = (data: any) => {
    // Show preview modal instead of directly submitting
    setPreviewData(data);
    setShowPreviewModal(true);
  };

  const handleFinalSubmit = async () => {
    if (!previewData) return;

    setLoading(true);
    try {
      const transactionRequest: TransactionCreateRequest = {
        submit: true,
        type: selectedType,
        amount: previewData.amount,
        currency: 'USD',
        notes: previewData.description,
        category: previewData.programCode,
        receipt_uri: previewData.receiptUri,
        recipient: previewData.recipient,
        split_with: previewData.splitWith || [],
      };

      const response = await apiService.createTransaction(transactionRequest);

      // Close modal and show success
      setShowPreviewModal(false);
      Alert.alert(
        'Success',
        'Transaction submitted successfully',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      console.error('Transaction submission error:', error);
      Alert.alert('Error', 'Failed to create transaction. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleClosePreview = () => {
    setShowPreviewModal(false);
    setPreviewData(null);
  };

  const handleSaveDraft = async (data: any) => {
    setLoading(true);
    try {
      const transactionRequest: TransactionCreateRequest = {
        submit: false,
        type: selectedType,
        amount: data.amount,
        currency: 'USD',
        notes: data.description,
        category: data.programCode,
        receipt_uri: data.receiptUri,
        split_with: data.splitWith || [],
      };

      const response = await apiService.createTransaction(transactionRequest);

      Alert.alert(
        'Draft Saved',
        'Transaction saved as draft',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      console.error('Draft save error:', error);
      Alert.alert('Error', 'Failed to save draft. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getFormTitle = () => {
    switch (selectedType) {
      case TransactionType.SPEND:
        return 'Log Cash Spent';
      case TransactionType.TRANSFER:
        return 'Log Cash Given';
      case TransactionType.RETURNED:
        return 'Log Cash Returned';
      default:
        return 'New Transaction';
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={styles.backButton}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.title}>{getFormTitle()}</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Transaction Form */}
      <TransactionForm
        type={selectedType}
        onSubmit={handleSubmit}
        onSaveDraft={handleSaveDraft}
        loading={loading && !showPreviewModal}
      />

      {/* Preview Modal */}
      {previewData && (
        <TransactionPreviewModal
          visible={showPreviewModal}
          onClose={handleClosePreview}
          onConfirm={handleFinalSubmit}
          data={previewData}
          type={selectedType}
          loading={loading}
          userRole={user?.role}
          selectedUsers={users}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.surface,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
    backgroundColor: Colors.background,
  },
  backButton: {
    color: Colors.primary,
    fontSize: 16,
    fontWeight: '600',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  placeholder: {
    width: 50,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: 16,
  },
  errorMessage: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
});
